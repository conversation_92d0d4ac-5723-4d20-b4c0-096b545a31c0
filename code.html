<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test GrapesJS Layout Blocks</title>

    <!-- GrapesJS CSS -->
    <link rel="stylesheet" href="https://unpkg.com/grapesjs/dist/css/grapes.min.css">

    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100vh;
            font-family: Arial, sans-serif;
        }

        .header {
            background: #2c3e50;
            color: white;
            padding: 10px 20px;
            text-align: center;
        }

        #gjs {
            height: calc(100vh - 60px);
            border: none;
        }
    </style>
</head>
<body>
<div class="header">
    <h2>Test GrapesJS với Layout Blocks</h2>
</div>

<div id="gjs"></div>

<!-- GrapesJS JavaScript -->
<script src="https://unpkg.com/grapesjs"></script>
<script src="https://unpkg.com/grapesjs-blocks-basic"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Initializing GrapesJS test...');

        try {
            console.log('GrapesJS available:', typeof grapesjs);

            const editor = grapesjs.init({
                container: '#gjs',
                height: '100%',
                width: 'auto',
                storageManager: false,

                // Use blocks-basic for layout blocks
                plugins: ['gjs-blocks-basic'],
                pluginsOpts: {
                    'gjs-blocks-basic': {
                        // Enable all layout blocks
                        blocks: ['column1', 'column2', 'column3', 'column3-7', 'text', 'link', 'image', 'video'],
                        category: 'Layout',
                        flexGrid: 1,
                        stylePrefix: 'gjs-',
                        addBasicStyle: true,
                        labelColumn1: '1 Column',
                        labelColumn2: '2 Columns',
                        labelColumn3: '3 Columns',
                        labelColumn37: '2 Columns 3/7',
                        labelText: 'Text',
                        labelLink: 'Link',
                        labelImage: 'Image',
                        labelVideo: 'Video',
                        rowHeight: 75,
                    }
                },

                // Canvas configuration
                canvas: {
                    styles: [
                        'https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css'
                    ]
                }
            });

            // Add additional layout blocks
            const blockManager = editor.BlockManager;

            // Add 4 Columns layout
            blockManager.add('column4', {
                label: '4 Columns',
                category: 'Layout',
                content: `
                        <div class="row">
                            <div class="col-3" style="min-height: 75px; padding: 10px; border: 1px dashed #ddd;">
                                <p style="text-align: center; color: #999; margin: 20px 0;">Column 1</p>
                            </div>
                            <div class="col-3" style="min-height: 75px; padding: 10px; border: 1px dashed #ddd;">
                                <p style="text-align: center; color: #999; margin: 20px 0;">Column 2</p>
                            </div>
                            <div class="col-3" style="min-height: 75px; padding: 10px; border: 1px dashed #ddd;">
                                <p style="text-align: center; color: #999; margin: 20px 0;">Column 3</p>
                            </div>
                            <div class="col-3" style="min-height: 75px; padding: 10px; border: 1px dashed #ddd;">
                                <p style="text-align: center; color: #999; margin: 20px 0;">Column 4</p>
                            </div>
                        </div>
                    `,
                attributes: { class: 'fa fa-th' }
            });

            console.log('GrapesJS initialized successfully');
            console.log('Available blocks:', editor.BlockManager.getAll().models.map(block => block.get('id')));

        } catch (error) {
            console.error('Error initializing GrapesJS:', error);
        }
    });
</script>
</body>
</html>
